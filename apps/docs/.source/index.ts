// @ts-nocheck -- skip type checking
import * as docs_19 from "../content/platform/usinglibra/deploy/index.zh.mdx?collection=docs&hash=1753606346424"
import * as docs_18 from "../content/platform/usinglibra/deploy/index.mdx?collection=docs&hash=1753606346424"
import * as docs_17 from "../content/platform/usinglibra/directEdit/index.zh.mdx?collection=docs&hash=1753606346424"
import * as docs_16 from "../content/platform/usinglibra/directEdit/index.mdx?collection=docs&hash=1753606346424"
import * as docs_15 from "../content/opensource/getstarted/index.zh.mdx?collection=docs&hash=1753606346424"
import * as docs_14 from "../content/opensource/getstarted/index.mdx?collection=docs&hash=1753606346424"
import * as docs_13 from "../content/opensource/getstarted/faq.zh.mdx?collection=docs&hash=1753606346424"
import * as docs_12 from "../content/opensource/getstarted/faq.mdx?collection=docs&hash=1753606346424"
import * as docs_11 from "../content/opensource/bestpractice/index.zh.mdx?collection=docs&hash=1753606346424"
import * as docs_10 from "../content/opensource/bestpractice/index.mdx?collection=docs&hash=1753606346424"
import * as docs_9 from "../content/platform/usinglibra/index.zh.mdx?collection=docs&hash=1753606346424"
import * as docs_8 from "../content/platform/usinglibra/index.mdx?collection=docs&hash=1753606346424"
import * as docs_7 from "../content/opensource/index.zh.mdx?collection=docs&hash=1753606346424"
import * as docs_6 from "../content/opensource/index.mdx?collection=docs&hash=1753606346424"
import * as docs_5 from "../content/platform/quickstart.zh.mdx?collection=docs&hash=1753606346424"
import * as docs_4 from "../content/platform/quickstart.mdx?collection=docs&hash=1753606346424"
import * as docs_3 from "../content/platform/index.zh.mdx?collection=docs&hash=1753606346424"
import * as docs_2 from "../content/platform/index.mdx?collection=docs&hash=1753606346424"
import * as docs_1 from "../content/platform/faq.zh.mdx?collection=docs&hash=1753606346424"
import * as docs_0 from "../content/platform/faq.mdx?collection=docs&hash=1753606346424"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"platform/faq.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/faq.mdx"}, data: docs_0 }, { info: {"path":"platform/faq.zh.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/faq.zh.mdx"}, data: docs_1 }, { info: {"path":"platform/index.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/index.mdx"}, data: docs_2 }, { info: {"path":"platform/index.zh.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/index.zh.mdx"}, data: docs_3 }, { info: {"path":"platform/quickstart.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/quickstart.mdx"}, data: docs_4 }, { info: {"path":"platform/quickstart.zh.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/quickstart.zh.mdx"}, data: docs_5 }, { info: {"path":"opensource/index.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/opensource/index.mdx"}, data: docs_6 }, { info: {"path":"opensource/index.zh.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/opensource/index.zh.mdx"}, data: docs_7 }, { info: {"path":"platform/usinglibra/index.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/usinglibra/index.mdx"}, data: docs_8 }, { info: {"path":"platform/usinglibra/index.zh.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/usinglibra/index.zh.mdx"}, data: docs_9 }, { info: {"path":"opensource/bestpractice/index.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/opensource/bestpractice/index.mdx"}, data: docs_10 }, { info: {"path":"opensource/bestpractice/index.zh.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/opensource/bestpractice/index.zh.mdx"}, data: docs_11 }, { info: {"path":"opensource/getstarted/faq.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/opensource/getstarted/faq.mdx"}, data: docs_12 }, { info: {"path":"opensource/getstarted/faq.zh.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/opensource/getstarted/faq.zh.mdx"}, data: docs_13 }, { info: {"path":"opensource/getstarted/index.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/opensource/getstarted/index.mdx"}, data: docs_14 }, { info: {"path":"opensource/getstarted/index.zh.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/opensource/getstarted/index.zh.mdx"}, data: docs_15 }, { info: {"path":"platform/usinglibra/directEdit/index.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/usinglibra/directEdit/index.mdx"}, data: docs_16 }, { info: {"path":"platform/usinglibra/directEdit/index.zh.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/usinglibra/directEdit/index.zh.mdx"}, data: docs_17 }, { info: {"path":"platform/usinglibra/deploy/index.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/usinglibra/deploy/index.mdx"}, data: docs_18 }, { info: {"path":"platform/usinglibra/deploy/index.zh.mdx","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/usinglibra/deploy/index.zh.mdx"}, data: docs_19 }], [{"info":{"path":"meta.json","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/meta.json"},"data":{"pages":["platform","opensource"]}}, {"info":{"path":"meta.zh.json","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/meta.zh.json"},"data":{"pages":["platform","opensource"]}}, {"info":{"path":"opensource/meta.json","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/opensource/meta.json"},"data":{"title":"Open Source","pages":["---Getting Started---","getstarted/index","getstarted/faq","---Best Practice---","bestpractice/index","---Open Source---","index"],"description":"Open source documentation and guides","root":true,"icon":"BookOpen"}}, {"info":{"path":"opensource/meta.zh.json","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/opensource/meta.zh.json"},"data":{"title":"开源","pages":["---快速开始---","getstarted/index","getstarted/faq","---最佳实践---","bestpractice/index","---开源---","index"],"description":"开源文档和指南","root":true,"icon":"BookOpen"}}, {"info":{"path":"platform/meta.json","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/meta.json"},"data":{"title":"Libra AI","pages":["---Quickstart---","index","quickstart","faq","---Using Libra---","usinglibra/index","usinglibra/deploy/index"],"description":"平台特定功能和集成","root":true,"icon":"Server"}}, {"info":{"path":"platform/meta.zh.json","absolutePath":"/Users/<USER>/opensource/libra/apps/docs/content/platform/meta.zh.json"},"data":{"title":"Libra AI","pages":["---快速开始---","index","quickstart","faq","---使用 Libra---","usinglibra/index","usinglibra/deploy/index"],"description":"平台特定功能和集成","root":true,"icon":"Server"}}])