---
title: "版本历史"
description: "了解如何管理、查看和恢复 Libra 项目的历史版本"
mode: "center"
icon: History
---

# 版本历史

了解如何管理、查看和恢复 Libra 项目的历史版本

## 管理版本历史

您在 Libra 中发送的每个提示都会创建项目的新版本。此版本历史功能允许您跟踪更改、查看以前的状态，并在需要时恢复到早期版本。

## 理解版本

每个版本都代表项目在特定时间点的快照。您可以：

- 查看以前的版本，了解项目当时的样子
- 在需要时恢复到早期版本
- 复制版本以处理多个变体

*版本管理界面 - 查看和与项目版本交互*

## 查看历史版本

"查看"选项让您可以探索以前的版本而不做任何更改。这非常适合：

- 检查项目在特定时间点的样子
- 截取以前设计的屏幕截图
- 比较不同的迭代

*查看历史版本*

## 恢复版本

当您恢复到以前的版本时：

- 您的项目返回到那个确切的状态
- 该时间点之后创建的所有版本都将被删除
- 您可以从该恢复点继续工作

*恢复到历史版本*

<Callout type="warn">
恢复版本是永久性的，无法撤销。恢复点之后创建的所有版本都将被删除。
</Callout>

## 处理多个版本

如果您想保留当前版本和历史版本：

1. 查看您感兴趣的版本
2. 使用"复制"功能创建新项目
3. 独立处理两个版本

*复制版本以处理多个变体*

<Callout type="info">
当您想要探索不同的设计方向或维护项目的多个变体时，请使用版本复制功能。
</Callout>

## 最佳实践

### 1. 定期检查点

进行增量、原子性更改，以便拥有良好、清晰的版本历史

### 2. 恢复前先查看

在恢复之前始终查看版本，以确保它是正确的版本

### 3. 使用复制功能

如有疑问，请复制而不是恢复，以保护您的工作
