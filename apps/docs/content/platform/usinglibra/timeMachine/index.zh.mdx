---
title: "版本历史"
description: "了解如何管理、查看和恢复 Libra 项目的历史版本"
mode: "center"
icon: History
---

# 版本历史

记录项目的所有变化，随时回到任何之前的版本

## 什么是版本历史？

把版本历史想象成项目的相册。每次您让 Libra 做修改时，它都会给项目拍一张"照片"（创建一个版本）。您可以随时翻看这些"照片"，甚至回到项目之前的样子。

## 版本历史能做什么？

有了版本历史，您可以：

- **回头看**：查看项目在任何时间点的样子
- **回到过去**：让项目回到之前的版本
- **制作副本**：复制任何版本来尝试不同的想法

*版本管理界面 - 查看和与项目版本交互*

## 查看旧版本

点击"查看"可以看到项目之前的样子 - 不会改变任何东西。这很适合：

- 看看项目昨天或上周是什么样子
- 给旧设计截图
- 比较不同版本，看看有什么变化

*查看历史版本*

## 回到旧版本

当您"恢复"到旧版本时，会发生这些事情：

- 您的项目回到那个时候的样子
- 那个时间点之后的所有新版本都会被永久删除
- 您可以从那个旧版本开始做新的修改

*回到旧版本*

<Callout type="warn">
⚠️ **重要提醒**：一旦回到过去，就无法撤销！之后的所有版本都会被永久删除。
</Callout>

## 安全地尝试不同想法

想要保留当前版本，同时尝试旧版本的内容？这样做：

1. 查看您喜欢的旧版本
2. 点击"复制"制作副本，创建新项目
3. 现在您有两个项目了 - 想改哪个就改哪个！

*制作旧版本的副本*

<Callout type="info">
💡 **小贴士**：制作副本非常适合尝试不同想法，而不会丢失当前的工作。
</Callout>

## 使用小贴士

### 1. 每次只改一点

一次只要求改一个东西，不要一次改很多。这样每个版本都很容易理解。

### 2. 先看再回去

在点击"恢复"之前，先点击"查看"确认这就是您想要的版本。

### 3. 不确定就复制

如果不确定，就制作副本而不是直接回去。这样可以保证所有东西都安全！
